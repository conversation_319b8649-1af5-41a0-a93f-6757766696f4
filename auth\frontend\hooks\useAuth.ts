// Re-export the useAuth hook from AuthProvider for convenience
export { useAuth } from '../components/AuthProvider';

// Additional auth-related hooks can be added here

import { useEffect, useState } from 'react';
import { authService } from '../services/authService';

/**
 * Hook to check if user has specific permissions
 */
export function usePermissions(requiredPermissions: string[]) {
  const [hasPermissions, setHasPermissions] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkPermissions = async () => {
      try {
        setIsLoading(true);
        const user = await authService.getCurrentUser();
        
        if (user && user.permissions) {
          const hasAll = requiredPermissions.every(permission => 
            user.permissions!.includes(permission)
          );
          setHasPermissions(hasAll);
        } else {
          setHasPermissions(false);
        }
      } catch (error) {
        console.error('Error checking permissions:', error);
        setHasPermissions(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkPermissions();
  }, [requiredPermissions]);

  return { hasPermissions, isLoading };
}

/**
 * Hook to check if user has specific role
 */
export function useRole(requiredRole: string) {
  const [hasRole, setHasRole] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkRole = async () => {
      try {
        setIsLoading(true);
        const user = await authService.getCurrentUser();
        setHasRole(user?.role === requiredRole);
      } catch (error) {
        console.error('Error checking role:', error);
        setHasRole(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkRole();
  }, [requiredRole]);

  return { hasRole, isLoading };
}
