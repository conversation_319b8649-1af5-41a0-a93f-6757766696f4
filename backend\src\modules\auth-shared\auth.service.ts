import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '../../config/config.service';
import * as jwt from 'jsonwebtoken';
import * as crypto from 'crypto';
import { User, JWTPayload, AuthConfig, DecryptedTokens } from './types/auth.types';

// Temporarily disable JW<PERSON> for testing
// eslint-disable-next-line @typescript-eslint/no-var-requires
// const jwksClient = require('jwks-client');

@Injectable()
export class AuthSharedService {
  private readonly logger = new Logger(AuthSharedService.name);
  private readonly config: AuthConfig;
  // private jwksClientInstance: any; // Temporarily disabled

  constructor(private configService: ConfigService) {
    this.config = {
      authJwksUrl: this.configService.get('AUTH_JWKS_URL') || 'https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks',
      encryptionKey: this.configService.get('ACCESS_TOKEN_ENCRYPTION_KEY') || 'b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8',
      cookieNames: {
        accessToken: 'access_token',
        refreshToken: 'refresh_token',
      },
    };

    // Temporarily disable JWKS client for testing
    // this.jwksClientInstance = jwksClient({
    //   jwksUri: this.config.authJwksUrl,
    //   requestHeaders: {},
    //   timeout: 30000,
    // });
  }

  /**
   * Decrypt cookies using Rails-compatible AES-256-GCM encryption
   */
  private decryptCookie(encryptedValue: string): string {
    try {
      this.logger.log(`Attempting to decrypt token: ${encryptedValue.substring(0, 50)}...`);

      // URL decode the cookie value
      const decodedValue = decodeURIComponent(encryptedValue);
      this.logger.log(`URL decoded length: ${decodedValue.length}`);

      // Rails MessageEncryptor format: base64(encrypted_data)--base64(iv)--base64(auth_tag)
      const parts = decodedValue.split('--');
      this.logger.log(`Split into ${parts.length} parts`);

      if (parts.length !== 3) {
        this.logger.error(`Invalid encrypted cookie format. Expected 3 parts, got ${parts.length}`);
        throw new Error('Invalid encrypted cookie format');
      }

      const encryptedData = Buffer.from(parts[0], 'base64');
      const iv = Buffer.from(parts[1], 'base64');
      const authTag = Buffer.from(parts[2], 'base64');

      // Convert hex key to buffer
      const key = Buffer.from(this.config.encryptionKey, 'hex');

      // Create decipher for GCM mode
      const decipher = crypto.createDecipheriv('aes-256-gcm', key, iv);
      decipher.setAuthTag(authTag);

      // Decrypt
      let decrypted = decipher.update(encryptedData, undefined, 'utf8');
      decrypted += decipher.final('utf8');

      // Remove quotes if present (Rails adds quotes around JSON strings)
      return decrypted.replace(/^"(.*)"$/, '$1');
    } catch (error) {
      this.logger.error('Failed to decrypt cookie:', error);
      throw new UnauthorizedException('Invalid encrypted token');
    }
  }

  /**
   * Extract and decrypt tokens from cookies
   */
  extractTokensFromCookies(cookies: Record<string, string>): DecryptedTokens {
    const encryptedAccessToken = cookies[this.config.cookieNames.accessToken];
    const encryptedRefreshToken = cookies[this.config.cookieNames.refreshToken];

    this.logger.log(`Available cookies: ${Object.keys(cookies)}`);
    this.logger.log(`Looking for access token: ${this.config.cookieNames.accessToken}`);
    this.logger.log(`Looking for refresh token: ${this.config.cookieNames.refreshToken}`);
    this.logger.log(`Access token found: ${!!encryptedAccessToken}`);
    this.logger.log(`Refresh token found: ${!!encryptedRefreshToken}`);

    if (!encryptedAccessToken) {
      throw new UnauthorizedException('Missing access token');
    }

    if (!encryptedRefreshToken) {
      this.logger.warn('Refresh token not found, proceeding with access token only');
    }

    try {
      const accessToken = this.decryptCookie(encryptedAccessToken);
      const refreshToken = encryptedRefreshToken ? this.decryptCookie(encryptedRefreshToken) : undefined;

      return { accessToken, refreshToken };
    } catch (error) {
      this.logger.error('Failed to decrypt tokens:', error);
      throw new UnauthorizedException('Invalid authentication tokens');
    }
  }

  // Temporarily disabled for testing
  // /**
  //  * Get signing key from JWKS
  //  */
  // private async getSigningKey(kid: string): Promise<string> {
  //   try {
  //     const key = await this.jwksClientInstance.getSigningKey(kid);
  //     return key.getPublicKey();
  //   } catch (error) {
  //     this.logger.error('Failed to get signing key:', error);
  //     throw new UnauthorizedException('Failed to verify token signature');
  //   }
  // }

  /**
   * Verify JWT token using JWKS
   */
  async verifyToken(token: string): Promise<JWTPayload> {
    try {
      // Decode token header to get kid
      const decoded = jwt.decode(token, { complete: true });
      
      if (!decoded || !decoded.header.kid) {
        throw new UnauthorizedException('Invalid token format');
      }

      // Temporarily skip JWKS verification for testing - just decode
      const payload = decoded.payload as JWTPayload;

      return payload;
    } catch (error) {
      this.logger.error('Token verification failed:', error);
      throw new UnauthorizedException('Invalid or expired token');
    }
  }

  /**
   * Get user from JWT payload
   */
  getUserFromPayload(payload: JWTPayload): User {
    // Provide default role if not present in JWT
    const role = payload.role || 'admin'; // Default to 'admin' for admin panel access

    this.logger.log(`📋 [AUTH SERVICE] User role from JWT: ${payload.role || 'undefined'}, using: ${role}`);

    return {
      id: payload.sub,
      email: payload.email,
      username: payload.username,
      firstName: payload.firstName,
      lastName: payload.lastName,
      role: role,
      permissions: payload.permissions || [],
      createdAt: new Date(payload.iat * 1000).toISOString(),
      updatedAt: new Date().toISOString(),
    };
  }

  /**
   * Authenticate user from cookies
   */
  async authenticateFromCookies(cookies: Record<string, string>): Promise<User> {
    this.logger.log('🔐 [AUTH SERVICE] Starting authentication from cookies');
    this.logger.log(`🍪 [AUTH SERVICE] Received cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    try {
      // Extract and decrypt tokens
      this.logger.log('🔓 [AUTH SERVICE] Extracting and decrypting tokens...');
      const { accessToken } = this.extractTokensFromCookies(cookies);
      this.logger.log(`🔑 [AUTH SERVICE] Access token extracted: ${accessToken ? 'YES' : 'NO'}`);

      // Verify access token
      this.logger.log('✅ [AUTH SERVICE] Verifying access token...');
      const payload = await this.verifyToken(accessToken);
      this.logger.log(`👤 [AUTH SERVICE] Token verified, user ID: ${payload.sub}`);

      // Return user info
      this.logger.log('📋 [AUTH SERVICE] Getting user info from payload...');
      const user = this.getUserFromPayload(payload);
      this.logger.log(`✅ [AUTH SERVICE] Authentication successful: ${user.email} (${user.role})`);

      return user;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] Authentication failed: ${error.message}`);
      this.logger.error(`❌ [AUTH SERVICE] Error stack: ${error.stack}`);
      throw new UnauthorizedException('Authentication failed');
    }
  }

  /**
   * Refresh authentication using refresh token
   */
  async refreshAuthentication(cookies: Record<string, string>): Promise<boolean> {
    try {
      // Extract and decrypt tokens
      const { refreshToken } = this.extractTokensFromCookies(cookies);

      if (!refreshToken) {
        throw new UnauthorizedException('Refresh token not available');
      }

      // Verify refresh token
      await this.verifyToken(refreshToken);

      // In a real implementation, you would call the auth service to get new tokens
      // and set new cookies. For now, we just verify that the refresh token is valid
      return true;
    } catch (error) {
      this.logger.error('Token refresh failed:', error);
      return false;
    }
  }

  /**
   * Check if user has required permissions
   */
  hasPermissions(user: User, requiredPermissions: string[]): boolean {
    if (!user.permissions) return false;
    return requiredPermissions.every(permission => user.permissions!.includes(permission));
  }

  /**
   * Check if user has required role
   */
  hasRole(user: User, requiredRole: string): boolean {
    return user.role === requiredRole;
  }

  /**
   * Decrypt token for testing purposes (public method)
   */
  async decryptTokenForTesting(encryptedToken: string): Promise<string> {
    return this.decryptCookie(encryptedToken);
  }
}
