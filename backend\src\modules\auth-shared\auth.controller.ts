import { Controller, Get, Post, Req, Res, UnauthorizedException, Logger, HttpStatus, Body } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiUnauthorizedResponse, ApiBody, ApiCookieAuth } from '@nestjs/swagger';
import { FastifyRequest, FastifyReply } from 'fastify';
import { AuthSharedService } from './auth.service';
import { Public } from './auth.guard';
import { DecryptTokenDto, DecodeJwtDto } from './dto/decrypt.dto';
import * as jwt from 'jsonwebtoken';

@ApiTags('Authentication')
@Controller('auth')
export class AuthSharedController {
  private readonly logger = new Logger(AuthSharedController.name);

  constructor(private readonly authSharedService: AuthSharedService) {}

  @Get('me/cookies')
  @ApiCookieAuth('access_token')
  @ApiCookieAuth('refresh_token')
  @ApiOperation({
    summary: 'Get current user from cookies',
    description: 'Authenticates user using encrypted cookies and returns user information'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User authenticated successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        email: { type: 'string' },
        username: { type: 'string' },
        firstName: { type: 'string' },
        lastName: { type: 'string' },
        role: { type: 'string' },
        permissions: { type: 'array', items: { type: 'string' } },
        createdAt: { type: 'string' },
        updatedAt: { type: 'string' }
      }
    }
  })
  @ApiUnauthorizedResponse({
    description: 'Authentication failed - invalid or missing cookies',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Authentication failed' }
      }
    }
  })
  async getCurrentUserFromCookies(
    @Req() request: FastifyRequest,
    @Res() reply: FastifyReply
  ) {
    try {
      // Extract cookies from request
      const cookies = (request as any).cookies || {};

      this.logger.log(`Received cookies: ${JSON.stringify(Object.keys(cookies))}`);
      this.logger.log(`Access token present: ${!!cookies.access_token}`);
      this.logger.log(`Refresh token present: ${!!cookies.refresh_token}`);

      // Authenticate user using cookies
      const user = await this.authSharedService.authenticateFromCookies(cookies);
      
      this.logger.log(`User authenticated via cookies: ${user.email}`);
      
      return reply.status(HttpStatus.OK).send(user);
    } catch (error) {
      this.logger.error('Cookie authentication failed:', error);
      throw new UnauthorizedException('Authentication failed');
    }
  }

  @Post('logout')
  @ApiCookieAuth('access_token')
  @ApiCookieAuth('refresh_token')
  @ApiOperation({
    summary: 'Logout user',
    description: 'Clears authentication cookies and logs out the user'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Logout successful',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Logout successful' }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Logout failed',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 500 },
        message: { type: 'string', example: 'Logout failed' }
      }
    }
  })
  async logout(
    @Req() request: FastifyRequest,
    @Res() reply: FastifyReply
  ) {
    try {
      this.logger.log('🔐 [AUTH CONTROLLER] Logout endpoint called');

      // Clear access_token cookie (Fastify method with type assertion)
      (reply as any).cookie('access_token', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        domain: process.env.COOKIE_DOMAIN || undefined,
        path: '/',
        expires: new Date(0) // Set to past date to clear
      });

      // Clear refresh_token cookie (Fastify method with type assertion)
      (reply as any).cookie('refresh_token', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        domain: process.env.COOKIE_DOMAIN || undefined,
        path: '/',
        expires: new Date(0) // Set to past date to clear
      });

      this.logger.log('✅ [AUTH CONTROLLER] Authentication cookies cleared successfully');

      return reply.status(HttpStatus.OK).send({
        success: true,
        message: 'Logout successful'
      });
    } catch (error) {
      this.logger.error(`❌ [AUTH CONTROLLER] Logout error: ${error.message}`);
      return reply.status(HttpStatus.INTERNAL_SERVER_ERROR).send({
        success: false,
        message: 'Logout failed'
      });
    }
  }

  @Post('refresh')
  @ApiCookieAuth('refresh_token')
  @ApiOperation({
    summary: 'Refresh authentication tokens',
    description: 'Validates refresh token from cookies and confirms authentication status'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Token refresh successful',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Authentication refreshed successfully' }
      }
    }
  })
  @ApiUnauthorizedResponse({
    description: 'Refresh failed - invalid or expired refresh token',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Token refresh failed' }
      }
    }
  })
  async refreshAuthentication(
    @Req() request: FastifyRequest,
    @Res() reply: FastifyReply
  ) {
    try {
      // Extract cookies from request
      const cookies = (request as any).cookies || {};
      
      // Attempt to refresh authentication
      const success = await this.authSharedService.refreshAuthentication(cookies);
      
      if (success) {
        this.logger.log('Authentication refreshed successfully');
        return reply.status(HttpStatus.OK).send({
          success: true,
          message: 'Authentication refreshed successfully'
        });
      } else {
        this.logger.warn('Authentication refresh failed');
        throw new UnauthorizedException('Token refresh failed');
      }
    } catch (error) {
      this.logger.error('Token refresh error:', error);
      throw new UnauthorizedException('Token refresh failed');
    }
  }

  @Get('me')
  @ApiOperation({ 
    summary: 'Get current user (legacy endpoint)',
    description: 'Legacy endpoint for getting current user - redirects to cookie-based auth'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User authenticated successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        email: { type: 'string' },
        username: { type: 'string' },
        firstName: { type: 'string' },
        lastName: { type: 'string' },
        role: { type: 'string' },
        permissions: { type: 'array', items: { type: 'string' } },
        createdAt: { type: 'string' },
        updatedAt: { type: 'string' }
      }
    }
  })
  @ApiUnauthorizedResponse({
    description: 'Authentication failed',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Authentication failed' }
      }
    }
  })
  async getCurrentUser(
    @Req() request: FastifyRequest,
    @Res() reply: FastifyReply
  ) {
    // Redirect to cookie-based authentication
    return this.getCurrentUserFromCookies(request, reply);
  }

  @Get('status')
  @Public()
  @ApiOperation({
    summary: 'Get authentication service status',
    description: 'Public endpoint to check if authentication service is running'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Authentication service status',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        service: { type: 'string', example: 'auth-shared' },
        timestamp: { type: 'string' },
        version: { type: 'string', example: '1.0.0' }
      }
    }
  })
  getAuthStatus() {
    return {
      status: 'ok',
      service: 'auth-shared',
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    };
  }

  @Get('debug/cookies')
  @Public()
  @ApiOperation({
    summary: 'Debug cookie information',
    description: 'Debug endpoint to see all cookies and headers for troubleshooting'
  })
  debugCookies(@Req() request: any) {
    return {
      cookies: request.cookies || {},
      headers: {
        cookie: request.headers.cookie,
        origin: request.headers.origin,
        referer: request.headers.referer,
        host: request.headers.host,
        'user-agent': request.headers['user-agent'],
      },
      url: request.url,
      method: request.method,
      timestamp: new Date().toISOString(),
    };
  }

  @Post('decrypt')
  @Public()
  @ApiOperation({
    summary: 'Decrypt encrypted token',
    description: 'Decrypt an encrypted access token or refresh token for testing purposes'
  })
  @ApiBody({ type: DecryptTokenDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Token decrypted successfully',
    schema: {
      type: 'object',
      properties: {
        decrypted: { type: 'string', description: 'The decrypted token' },
        type: { type: 'string', description: 'Type of token (jwt or hex)' }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid encrypted token format'
  })
  async decryptToken(@Body() body: DecryptTokenDto) {
    try {
      const decrypted = await this.authSharedService.decryptTokenForTesting(body.token);

      // Try to determine if it's a JWT or hex token
      let type = 'unknown';
      if (decrypted.startsWith('eyJ')) {
        type = 'jwt';
      } else if (/^[0-9a-fA-F]+$/.test(decrypted)) {
        type = 'hex';
      }

      return {
        decrypted,
        type,
        length: decrypted.length
      };
    } catch (error) {
      this.logger.error('Failed to decrypt token:', error);
      throw new UnauthorizedException('Failed to decrypt token');
    }
  }

  @Post('decode-jwt')
  @Public()
  @ApiOperation({
    summary: 'Decode JWT token',
    description: 'Decode a JWT token to see its payload (without verification)'
  })
  @ApiBody({ type: DecodeJwtDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'JWT decoded successfully',
    schema: {
      type: 'object',
      properties: {
        header: { type: 'object', description: 'JWT header' },
        payload: { type: 'object', description: 'JWT payload' },
        signature: { type: 'string', description: 'JWT signature' }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid JWT format'
  })
  decodeJwt(@Body() body: DecodeJwtDto) {
    try {
      const decoded = jwt.decode(body.jwt, { complete: true });

      if (!decoded) {
        throw new Error('Invalid JWT format');
      }

      return {
        header: decoded.header,
        payload: decoded.payload,
        signature: decoded.signature
      };
    } catch (error) {
      this.logger.error('Failed to decode JWT:', error);
      throw new UnauthorizedException('Failed to decode JWT');
    }
  }


}
