import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AuthService } from './auth.service';
import { AuthGuard } from './auth.guard';
import { AuthMiddleware } from './auth.middleware';
import { AuthController } from './auth.controller';
import { AuthResolver } from './graphql/auth.resolver';

@Module({
  imports: [ConfigModule],
  providers: [AuthService, AuthGuard, AuthMiddleware, AuthResolver],
  controllers: [AuthController],
  exports: [AuthService, AuthGuard, AuthMiddleware, AuthResolver],
})
export class AuthModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(AuthMiddleware)
      .forRoutes('*'); // Apply to all routes
  }
}

// For manual integration without NestJS module system
export class AuthModuleStandalone {
  private authService: AuthService;
  private authGuard: AuthGuard;

  constructor(config: {
    authJwksUrl?: string;
    encryptionKey?: string;
  }) {
    // Mock ConfigService for standalone usage
    const configService = {
      get: (key: string) => {
        switch (key) {
          case 'AUTH_JWKS_URL':
            return config.authJwksUrl || process.env.AUTH_JWKS_URL;
          case 'ACCESS_TOKEN_ENCRYPTION_KEY':
            return config.encryptionKey || process.env.ACCESS_TOKEN_ENCRYPTION_KEY;
          default:
            return process.env[key];
        }
      },
    } as any;

    this.authService = new AuthService(configService);
    // Note: AuthGuard requires Reflector which is NestJS specific
    // For standalone usage, use AuthService directly
  }

  getAuthService(): AuthService {
    return this.authService;
  }

  getAuthGuard(): AuthGuard {
    return this.authGuard;
  }
}

// Export everything for easy importing
export * from './auth.service';
export * from './auth.guard';
export * from './auth.middleware';
export * from './types/auth.types';
export * from './graphql';
