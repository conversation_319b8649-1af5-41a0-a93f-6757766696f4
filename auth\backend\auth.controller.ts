// NOTE: This file is part of the reusable auth module
// Make sure to adjust import paths and dependencies based on your project structure
import { Controller, Get, Post, Req, Res, UnauthorizedException, Logger, HttpStatus, Body } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiUnauthorizedResponse, ApiBody, ApiCookieAuth } from '@nestjs/swagger';
import { FastifyRequest, FastifyReply } from 'fastify';
import { AuthService } from './auth.service';
import { Public } from './auth.guard';
import { DecryptTokenDto, DecodeJwtDto } from './dto/decrypt.dto';
import * as jwt from 'jsonwebtoken';

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(private readonly authService: AuthService) {}

  @Get('me/cookies')
  @ApiCookieAuth('access_token')
  @ApiCookieAuth('refresh_token')
  @ApiOperation({ 
    summary: 'Get current user from cookies',
    description: 'Authenticates user using encrypted cookies and returns user information'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User authenticated successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        email: { type: 'string' },
        username: { type: 'string' },
        firstName: { type: 'string' },
        lastName: { type: 'string' },
        role: { type: 'string' },
        permissions: { type: 'array', items: { type: 'string' } },
        createdAt: { type: 'string' },
        updatedAt: { type: 'string' }
      }
    }
  })
  @ApiUnauthorizedResponse({
    description: 'Authentication failed - invalid or missing cookies',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
        error: { type: 'string' },
        statusCode: { type: 'number' }
      }
    }
  })
  async getCurrentUserFromCookies(
    @Req() request: FastifyRequest,
    @Res() reply: FastifyReply
  ) {
    try {
      // Extract cookies from request
      const cookies = (request as any).cookies || {};

      this.logger.log(`Received cookies: ${JSON.stringify(Object.keys(cookies))}`);
      this.logger.log(`Access token present: ${!!cookies.access_token}`);
      this.logger.log(`Refresh token present: ${!!cookies.refresh_token}`);

      // Authenticate user using cookies
      const user = await this.authService.authenticateFromCookies(cookies);
      
      return reply.status(HttpStatus.OK).send(user);
    } catch (error) {
      this.logger.error('Cookie authentication failed:', error);
      throw new UnauthorizedException('Authentication failed');
    }
  }

  @Post('logout')
  @ApiCookieAuth('access_token')
  @ApiCookieAuth('refresh_token')
  @ApiOperation({
    summary: 'Logout user',
    description: 'Clears authentication cookies and logs out the user'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Logout successful',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Logout successful' }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Logout failed',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 500 },
        message: { type: 'string', example: 'Logout failed' }
      }
    }
  })
  async logout(
    @Req() request: FastifyRequest,
    @Res() reply: FastifyReply
  ) {
    try {
      this.logger.log('🔐 [AUTH CONTROLLER] Logout endpoint called');

      // Clear access_token cookie (Fastify method with type assertion)
      (reply as any).cookie('access_token', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        domain: process.env.COOKIE_DOMAIN || undefined,
        path: '/',
        expires: new Date(0) // Set to past date to clear
      });

      // Clear refresh_token cookie (Fastify method with type assertion)
      (reply as any).cookie('refresh_token', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        domain: process.env.COOKIE_DOMAIN || undefined,
        path: '/',
        expires: new Date(0) // Set to past date to clear
      });

      this.logger.log('✅ [AUTH CONTROLLER] Authentication cookies cleared successfully');

      return reply.status(HttpStatus.OK).send({
        success: true,
        message: 'Logout successful'
      });
    } catch (error) {
      this.logger.error(`❌ [AUTH CONTROLLER] Logout error: ${error.message}`);
      return reply.status(HttpStatus.INTERNAL_SERVER_ERROR).send({
        success: false,
        message: 'Logout failed'
      });
    }
  }

  @Post('refresh')
  @ApiCookieAuth('refresh_token')
  @ApiOperation({
    summary: 'Refresh authentication tokens',
    description: 'Validates refresh token from cookies and confirms authentication status'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Token refresh successful',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Authentication refreshed successfully' }
      }
    }
  })
  @ApiUnauthorizedResponse({
    description: 'Refresh failed - invalid or expired refresh token',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
        error: { type: 'string' },
        statusCode: { type: 'number' }
      }
    }
  })
  async refreshTokens(
    @Req() request: FastifyRequest,
    @Res() reply: FastifyReply
  ) {
    try {
      // Extract cookies from request
      const cookies = (request as any).cookies || {};
      
      // Refresh authentication
      const success = await this.authService.refreshTokens(cookies);
      
      if (success) {
        return reply.status(HttpStatus.OK).send({
          success: true,
          message: 'Authentication refreshed successfully'
        });
      } else {
        throw new UnauthorizedException('Failed to refresh authentication');
      }
    } catch (error) {
      this.logger.error('Token refresh failed:', error);
      throw new UnauthorizedException('Failed to refresh authentication');
    }
  }

  @Get('me')
  @ApiOperation({ 
    summary: 'Get current user (legacy endpoint)',
    description: 'Legacy endpoint for getting current user - redirects to cookie-based auth'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User authenticated successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        email: { type: 'string' },
        username: { type: 'string' },
        firstName: { type: 'string' },
        lastName: { type: 'string' },
        role: { type: 'string' },
        permissions: { type: 'array', items: { type: 'string' } },
        createdAt: { type: 'string' },
        updatedAt: { type: 'string' }
      }
    }
  })
  @ApiUnauthorizedResponse({
    description: 'Authentication failed'
  })
  async getCurrentUser(
    @Req() request: FastifyRequest,
    @Res() reply: FastifyReply
  ) {
    // Redirect to cookie-based endpoint
    return this.getCurrentUserFromCookies(request, reply);
  }

  @Get('status')
  @Public()
  @ApiOperation({ 
    summary: 'Get auth service status',
    description: 'Public endpoint to check if the auth service is running'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Service status',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string' },
        service: { type: 'string' },
        timestamp: { type: 'string' },
        version: { type: 'string' }
      }
    }
  })
  getAuthStatus() {
    return {
      status: 'ok',
      service: 'auth-shared',
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    };
  }

  @Post('decrypt')
  @Public()
  @ApiOperation({ 
    summary: 'Decrypt encrypted token',
    description: 'Decrypt an encrypted access token or refresh token for testing purposes'
  })
  @ApiBody({ type: DecryptTokenDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Token decrypted successfully',
    schema: {
      type: 'object',
      properties: {
        decrypted: { type: 'string', description: 'The decrypted token' },
        type: { type: 'string', description: 'Type of token (jwt or hex)' }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid encrypted token format'
  })
  async decryptToken(@Body() body: DecryptTokenDto) {
    try {
      const decrypted = await this.authService.decryptTokenForTesting(body.token);
      
      // Try to determine if it's a JWT or hex token
      let type = 'unknown';
      if (decrypted.startsWith('eyJ')) {
        type = 'jwt';
      } else if (/^[0-9a-fA-F]+$/.test(decrypted)) {
        type = 'hex';
      }

      return {
        decrypted,
        type,
        length: decrypted.length
      };
    } catch (error) {
      this.logger.error('Failed to decrypt token:', error);
      throw new UnauthorizedException('Failed to decrypt token');
    }
  }

  @Post('decode-jwt')
  @Public()
  @ApiOperation({ 
    summary: 'Decode JWT token',
    description: 'Decode a JWT token to see its payload (without verification)'
  })
  @ApiBody({ type: DecodeJwtDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'JWT decoded successfully',
    schema: {
      type: 'object',
      properties: {
        header: { type: 'object', description: 'JWT header' },
        payload: { type: 'object', description: 'JWT payload' },
        signature: { type: 'string', description: 'JWT signature' }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid JWT format'
  })
  decodeJwt(@Body() body: DecodeJwtDto) {
    try {
      const decoded = jwt.decode(body.jwt, { complete: true });
      
      if (!decoded) {
        throw new Error('Invalid JWT format');
      }

      return {
        header: decoded.header,
        payload: decoded.payload,
        signature: decoded.signature
      };
    } catch (error) {
      this.logger.error('Failed to decode JWT:', error);
      throw new UnauthorizedException('Failed to decode JWT');
    }
  }
}
