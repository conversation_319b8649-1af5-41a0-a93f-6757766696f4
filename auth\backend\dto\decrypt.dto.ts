import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

export class DecryptTokenDto {
  @ApiProperty({
    description: 'Encrypted token to decrypt',
    example: 'YZr5vwW8IYs7mmhYBggrJ%2BxdzS9K6Yq81vEjPdjZ1bWto97uyjDW6VVivJmqMSwFnZjPuOBXghxHVafy%2FQSbQonFVR8yvi3Imb1qnudPhfb1vvjdG%2FsDxWi7GsJSQ%2FXzUiU8EvlUKjxANqiYKg9SQXVrpbHquTFjdbMzgebBiQCNqg3AZuGoEqfKuQeZFHxDSdHU8SiKpZMVHBUdSUEyVpqCoCi6C0hIIksn30qHHO8KJzf%2Bt1KB5hKzJ8n%2B9QIMoIgSNp6yrPJsXGeRDHpp9NcmdqoUS8eHdo%2B2bxTksNUUTJL3%2BEBHVORSUf9neaDEgE85HLUGltsnD7RNUiX3i1nsXcoo6A%2F6eaclrsfxTteSydNuKdkoH7C6C%2Fww2%2BRNlo9341t8H22WHFaVxiKoUhBGah2dvhFNfQcWwSh%2F0sbLh0p%2FVh6ij9Bx1f183VyHsWaZ1kMf7gBhSXZeJomZK58M6PX3wETLd3yPNJKZ7Wj8ML1tuJAvaY8MM5KQYiBZM8I6JJ52fneZvrqjQPx6YpyQ93F9oFFZQ8DbI3%2BQWRjGbJHljOmXlYkrxKmNvzYFOW%2BVL3zF0own7Jmd3nGMUwQ1mQTcXn65t0h2gAapmEI3P%2FP05RKrLDUUQkqo9cn916yWEV8%2Fkph3KoRShRNrTh88W56DTCHRjitWiNdXN1x778LOksrHhX2USmTvvshEkB7CKoB6zmMRlaky0XKjmGAV%2BYlhGd7w8A3exmEgVymLxsc1MT%2BBq%2FFMzr9XJrBHnDi3Rm0jV1pAvBz3%2FfAysjZCpXMkOJVGYb9ozVV8jWFBuanRVKlnl%2FP%2F35XschMuOGr2qDdXC7ELccnFmlraQrNVtBLr8DLqcJ8oVtz4%2FDI%2FbYYsze%2F5d0GHMkEFqzei1zmmOfTWD5ka3rwLdtftyOBb39V%2BdD4cuG1orIBH8oGH3wrz0EmGTYZn1t3hqoXPgWipFZqAdiBf4O8Y--pIZXctUGl42jUtqj--AzrC4y%2FewTV3l4AqRdduBw%3D%3D'
  })
  @IsString()
  @IsNotEmpty()
  token: string;
}

export class DecodeJwtDto {
  @ApiProperty({
    description: 'JWT token to decode',
    example: 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjEyMyJ9.***************************************************************************************************************************************************************************************************************'
  })
  @IsString()
  @IsNotEmpty()
  jwt: string;
}
