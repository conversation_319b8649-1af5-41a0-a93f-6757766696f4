import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { ConfigModule } from '../../config/config.module';
import { AuthSharedService } from './auth.service';
import { AuthSharedGuard } from './auth.guard';
import { AuthSharedMiddleware } from './auth.middleware';
import { AuthSharedController } from './auth.controller';
import { ApiGuard, AdminGuard } from './guards';

@Module({
  imports: [ConfigModule],
  providers: [AuthSharedService, AuthSharedGuard, AuthSharedMiddleware, ApiGuard, AdminGuard],
  controllers: [AuthSharedController],
  exports: [AuthSharedService, AuthSharedGuard, AuthSharedMiddleware, ApiGuard, AdminGuard],
})
export class AuthSharedModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(AuthSharedMiddleware)
      .forRoutes('*'); // Apply to all routes
  }
}

// For manual integration without NestJS module system
export class AuthModuleStandalone {
  private authService: AuthSharedService;
  private authGuard: AuthSharedGuard;

  constructor(config: {
    authJwksUrl?: string;
    encryptionKey?: string;
  }) {
    // Mock ConfigService for standalone usage
    const configService = {
      get: (key: string) => {
        switch (key) {
          case 'AUTH_JWKS_URL':
            return config.authJwksUrl || process.env.AUTH_JWKS_URL;
          case 'ACCESS_TOKEN_ENCRYPTION_KEY':
            return config.encryptionKey || process.env.ACCESS_TOKEN_ENCRYPTION_KEY;
          default:
            return process.env[key];
        }
      },
    } as any;

    this.authService = new AuthSharedService(configService);
    // Note: AuthGuard requires Reflector which is NestJS specific
    // For standalone usage, use AuthService directly
  }

  getAuthService(): AuthSharedService {
    return this.authService;
  }

  getAuthGuard(): AuthSharedGuard {
    return this.authGuard;
  }
}

// Export everything for easy importing
export * from './auth.service';
export { AuthSharedGuard, Roles, Permissions, Public } from './auth.guard';
export * from './auth.middleware';
export * from './auth.controller';
export * from './guards';
export { getCurrentUser } from './decorators/user.decorator';
export * from './types/auth.types';
export * from './types';
