import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { AuthService } from './auth.service';
import { User } from './types/auth.types';

// Extend Express Request type to include user
declare global {
  namespace Express {
    interface Request {
      user?: User;
    }
  }
}

@Injectable()
export class AuthMiddleware implements NestMiddleware {
  private readonly logger = new Logger(AuthMiddleware.name);

  constructor(private authService: AuthService) {}

  async use(req: Request, res: Response, next: NextFunction) {
    try {
      // Skip authentication for public routes
      if (this.isPublicRoute(req.path)) {
        return next();
      }

      // Extract cookies
      const cookies = req.cookies || {};

      // Try to authenticate user
      try {
        const user = await this.authService.authenticateFromCookies(cookies);
        req.user = user;
        this.logger.debug(`User authenticated: ${user.email}`);
      } catch (error) {
        // Don't throw error here, let the guard handle it
        this.logger.debug('Authentication failed in middleware:', error.message);
      }

      next();
    } catch (error) {
      this.logger.error('Auth middleware error:', error);
      next();
    }
  }

  private isPublicRoute(path: string): boolean {
    const publicRoutes = [
      '/health',
      '/api/health',
      '/api/auth/login',
      '/api/auth/logout',
      '/api/auth/callback',
      // Add other public routes as needed
    ];

    return publicRoutes.some(route => path.startsWith(route));
  }
}

// Optional: Express middleware function version
export function authMiddleware(authService: AuthService) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const cookies = req.cookies || {};
      
      try {
        const user = await authService.authenticateFromCookies(cookies);
        req.user = user;
      } catch (error) {
        // User not authenticated, but don't block the request
        // Let the guard or controller handle authentication requirements
      }

      next();
    } catch (error) {
      console.error('Auth middleware error:', error);
      next();
    }
  };
}
