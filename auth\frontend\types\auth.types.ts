export interface User {
  id: string;
  email: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  role: string;
  permissions?: string[];
  createdAt: string;
  updatedAt: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface AuthContextType extends AuthState {
  login: (redirectUrl?: string) => void;
  logout: () => void;
  refreshAuth: () => Promise<void>;
}

export interface AuthConfig {
  authFrontendUrl: string;
  authJwksUrl: string;
  encryptionKey: string;
  cookieNames: {
    accessToken: string;
    refreshToken: string;
  };
  domains?: {
    cookieDomain?: string;
    allowedOrigins?: string[];
  };
}
